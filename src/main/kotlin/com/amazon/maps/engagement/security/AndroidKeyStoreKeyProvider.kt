package com.amazon.maps.engagement.security

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import com.amazon.majixplatform.log.logDebug
import com.amazon.majixplatform.log.logError
import com.amazon.majixplatform.log.logVerbose
import java.security.KeyStore
import java.security.SecureRandom
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject
import androidx.core.content.edit

/**
 * Implementation of [EncryptionKeyAPI] that uses Android Keystore system
 * for secure key storage and management.
 */
class AndroidKeyStoreKeyProvider @Inject constructor(
    private val context: Context,
    private val keyAlias: String = DEFAULT_KEY_ALIAS
) : EncryptionKeyAPI {

    private var keyStore: KeyStore? = null
    private var currentKey: SecretKey? = null
    private var isInitialized = false

    override fun initialize() {
        if (isInitialized) {
            return
        }

        try {
            logVerbose { "Initializing AndroidKeyStoreKeyProvider" }
            keyStore = KeyStore.getInstance(ANDROID_KEYSTORE).apply {
                load(null)
            }
            val keyAvailable = isKeyAvailable()
            logVerbose { "Key available: $keyAvailable" }
            if (!keyAvailable) {
                logVerbose { "Generating new key" }
                val success = generateNewKey()
                logVerbose { "Key generation success: $success" }
                if (!success) {
                    logError { "Failed to generate new key" }
                    return
                }
            }
            isInitialized = true
            logVerbose { "Initialization complete" }
        } catch (e: Exception) {
            logError(e) { "Failed to initialize KeyStore: $e" }
        }
    }

    override fun isKeyAvailable(): Boolean {
        return try {
            val hasAlias = keyStore?.containsAlias(keyAlias) == true
            logVerbose { "KeyStore has alias: $hasAlias" }

            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val hasKey = prefs.getString(ENCRYPTED_KEY_PREF, null) != null
            val hasIv = prefs.getString(IV_PREF, null) != null
            logDebug { "SharedPreferences has key: $hasKey, has IV: $hasIv" }

            hasAlias && hasKey && hasIv
        } catch (e: Exception) {
            logError(e) { "Error checking key availability" }
            false
        }
    }

    override fun getKey(): String? {
        if (!isInitialized) {
            logError { "Not initialized, initializing now" }
            initialize()
        }

        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val encryptedKey = prefs.getString(ENCRYPTED_KEY_PREF, null) ?: return null
            val iv = prefs.getString(IV_PREF, null) ?: return null

            val keystoreKey = keyStore?.getKey(keyAlias, null) as? SecretKey ?: return null

            // Decrypt the stored key using the Android Keystore key
            val decryptCipher = Cipher.getInstance(TRANSFORMATION)
            decryptCipher.init(Cipher.DECRYPT_MODE, keystoreKey, IvParameterSpec(Base64.decode(iv, Base64.DEFAULT)))
            val decryptedKey = decryptCipher.doFinal(Base64.decode(encryptedKey, Base64.DEFAULT))

            // Return the key as a Base64 encoded string
            Base64.encodeToString(decryptedKey, Base64.DEFAULT)
        } catch (e: Exception) {
            logError(e) { "Error getting key" }
            null
        }
    }

    fun getCipher(mode: Int): Cipher? {
        if (!isInitialized) {
            logError { "Not initialized, initializing now" }
            initialize()
        }

        return try {
            val keyString = getKey() ?: return null
            val keyBytes = Base64.decode(keyString, Base64.DEFAULT)

            val secretKey = SecretKeySpec(keyBytes, "AES")
            val cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
            cipher.init(mode, secretKey)
            cipher
        } catch (e: Exception) {
            logError(e) { "Error creating cipher" }
            null
        }
    }

    override fun refreshKey(): Boolean {
        return try {
            if (keyStore?.containsAlias(keyAlias) == true) {
                keyStore?.deleteEntry(keyAlias)
            }
            generateNewKey()
            true
        } catch (e: Exception) {
            logError(e) { "Error refreshing key" }
            false
        }
    }

    override fun isBackupStorageKeyValid(key: String): Boolean {
        // Implement backup key validation logic if needed
        return key.isNotEmpty()
    }

    override fun updateBackupStorageKey(key: String): Boolean {
        // Implement backup key update logic if needed
        return isBackupStorageKeyValid(key)
    }

    override fun reset() {
        try {
            if (keyStore?.containsAlias(keyAlias) == true) {
                keyStore?.deleteEntry(keyAlias)
            }
            context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .edit {
                    remove(ENCRYPTED_KEY_PREF)
                        .remove(IV_PREF)
                }
            currentKey = null
        } catch (e: Exception) {
            logError(e) { "Error resetting key manager" }
        }
    }

    override fun getStrategy(): EncryptionKeyAPI.RetrievalStrategy {
        return EncryptionKeyAPI.RetrievalStrategy.ANDROID_KEYSTORE
    }

    private fun generateNewKey(): Boolean {
        return try {
            // Generate Android Keystore key for protecting our encryption key
            val keyGenerator = KeyGenerator.getInstance(
                KeyProperties.KEY_ALGORITHM_AES,
                ANDROID_KEYSTORE
            )

            val keyGenSpec = KeyGenParameterSpec.Builder(
                keyAlias,
                KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
            )
                .setBlockModes(KeyProperties.BLOCK_MODE_CBC)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_PKCS7)
                .setRandomizedEncryptionRequired(true) // Let Android KeyStore handle IV generation
                .setKeySize(256)
                .build()

            keyGenerator.init(keyGenSpec)
            val keystoreKey = keyGenerator.generateKey()

            // Generate a random key for actual encryption
            val random = SecureRandom()
            val keyBytes = ByteArray(32) // 256 bits
            random.nextBytes(keyBytes)
            currentKey = SecretKeySpec(keyBytes, "AES")

            // Encrypt the random key with the Android Keystore key
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, keystoreKey)
            val encryptedKey = cipher.doFinal(keyBytes)

            // Get the IV that was generated by the cipher
            val iv = cipher.iv

            // Store the encrypted key and IV
            context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .edit {
                    putString(ENCRYPTED_KEY_PREF, Base64.encodeToString(encryptedKey, Base64.DEFAULT))
                        .putString(IV_PREF, Base64.encodeToString(iv, Base64.DEFAULT))
                }

            val storedIv = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .getString(IV_PREF, null)
            if (storedIv == null) {
                logError { "Failed to store IV in SharedPreferences" }
                return false
            }

            true
        } catch (e: Exception) {
            logError(e) { "Error generating new key (${e.message})" }
            false
        }
    }

    companion object {
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val DEFAULT_KEY_ALIAS = "maps_engagement_encryption_key"
        private const val PREFS_NAME = "maps_engagement_encryption"
        private const val ENCRYPTED_KEY_PREF = "encrypted_key"
        private const val IV_PREF = "iv"
        private const val TRANSFORMATION = "AES/CBC/PKCS7Padding"
    }
}
