package com.amazon.maps.engagement.security

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.amazon.majixplatform.log.logError
import com.amazon.majixplatform.log.logVerbose
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.security.InvalidKeyException
import java.security.NoSuchAlgorithmException
import javax.crypto.Cipher
import javax.crypto.CipherInputStream
import javax.crypto.CipherOutputStream
import javax.crypto.NoSuchPaddingException
import javax.inject.Inject

/**
 * Encryption Manager for encrypting images using [EncryptionKeyAPI].
 * This class provides methods to encrypt and decrypt image files using AES encryption.
 */
class ImageEncryptionManager @Inject constructor(
    private val encryptionKeyAPI: EncryptionKeyAPI
) {
    init {
        logVerbose { "Initializing ImageEncryptionManager" }
        encryptionKeyAPI.initialize()
        val keyAvailable = encryptionKeyAPI.isKeyAvailable()
        logVerbose { "Key available after initialization: $keyAvailable" }
        if (!keyAvailable) {
            logVerbose { "Key not available, refreshing" }
            val refreshSuccess = encryptionKeyAPI.refreshKey()
            logVerbose { "Key refresh success: $refreshSuccess" }
            if (!refreshSuccess) {
                logError { "Failed to refresh key" }
            }
        }
    }

    @SuppressLint("GetInstance")
    private fun getCipher(mode: Int): Cipher {
        return (encryptionKeyAPI as? AndroidKeyStoreKeyProvider)?.getCipher(mode)
            ?: throw IllegalStateException("Encryption key not available")
    }

    private fun getEncryptedFile(filePath: String): File {
        return when {
            !filePath.endsWith(FILE_EXTENSION) -> {
                File("$filePath.$FILE_EXTENSION")
            }

            else -> {
                File(filePath)
            }
        }
    }

    /**
     * Encrypt the file at given path using AES encryption.
     *
     * Perform this operation on background thread.
     *
     * @param filePath file that needs to be encrypted
     * @return if the file has been encrypted and stored
     */
    @Throws(
        IOException::class,
        NoSuchAlgorithmException::class,
        NoSuchPaddingException::class,
        InvalidKeyException::class
    )
    fun encryptImage(filePath: String): Boolean {
        val fileInputStream = FileInputStream(File(filePath))
        return encryptImage(filePath, fileInputStream)
    }

    /**
     * Encrypt the image at given path using AES encryption.
     *
     * Perform this operation on background thread.
     *
     * @param filePath file that needs to be encrypted
     * @param fileInputStream fileInputStream that will be encrypted
     * @return if the file has been encrypted and stored
     */
    @Throws(
        IOException::class,
        NoSuchAlgorithmException::class,
        NoSuchPaddingException::class,
        InvalidKeyException::class
    )
    fun encryptImage(
        filePath: String,
        fileInputStream: InputStream
    ): Boolean {
        val encryptedFile = getEncryptedFile(filePath)
        val fileOutputStream = FileOutputStream(encryptedFile)

        var cipherOutputStream: CipherOutputStream? = null
        try {
            // Get the Cipher instance with IV already initialized
            val cipher = getCipher(Cipher.ENCRYPT_MODE)

            // Generate the output from the cipher
            cipherOutputStream = CipherOutputStream(fileOutputStream, cipher)
            fileInputStream.copyTo(cipherOutputStream)
        } finally {
            cipherOutputStream?.flush()
            cipherOutputStream?.close()
            fileInputStream.close()
        }
        return encryptedFile.exists()
    }

    /**
     * Decrypt the image at given path using AES encryption.
     *
     * Perform this operation on background thread.
     *
     * @param filePath file that needs to be decrypted
     * @return decrypted image's Bitmap
     */
    @Throws(
        IOException::class,
        NoSuchAlgorithmException::class,
        NoSuchPaddingException::class,
        InvalidKeyException::class
    )
    fun decryptImage(filePath: String): Bitmap? {
        val encryptedFile = getEncryptedFile(filePath)
        val fileInputStream = FileInputStream(encryptedFile)
        var cipherInputStream: CipherInputStream? = null
        val bitmap: Bitmap?
        try {
            // Get cipher initialized for decryption
            val cipher = getCipher(Cipher.DECRYPT_MODE)

            cipherInputStream = CipherInputStream(fileInputStream, cipher)
            bitmap = BitmapFactory.decodeStream(cipherInputStream)
            cipherInputStream.close()
        } finally {
            cipherInputStream?.close()
            fileInputStream.close()
        }
        return bitmap
    }

    /**
     * Decrypt the image at given path using AES encryption.
     *
     * Perform this operation on background thread.
     *
     * @param filePath file that needs to be decrypted
     * @return decrypted image as a byte array
     */
    @Throws(
        IOException::class,
        NoSuchAlgorithmException::class,
        NoSuchPaddingException::class,
        InvalidKeyException::class
    )
    fun decryptImageToByteArray(filePath: String): ByteArray {
        val encryptedFile = getEncryptedFile(filePath)
        val fileInputStream = FileInputStream(encryptedFile)
        var cipherInputStream: CipherInputStream? = null
        val byteArray: ByteArray
        try {
            val cipher = getCipher(Cipher.DECRYPT_MODE)

            cipherInputStream = CipherInputStream(fileInputStream, cipher)
            byteArray = cipherInputStream.readBytes()
            cipherInputStream.close()
        } finally {
            cipherInputStream?.close()
            fileInputStream.close()
        }
        return byteArray
    }

    companion object {
        const val FILE_EXTENSION = "encrypted"
    }
}
