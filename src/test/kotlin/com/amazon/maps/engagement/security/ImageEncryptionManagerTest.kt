package com.amazon.maps.engagement.security

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import java.io.ByteArrayInputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.security.InvalidKeyException
import java.security.NoSuchAlgorithmException
import javax.crypto.Cipher
import javax.crypto.CipherInputStream
import javax.crypto.CipherOutputStream
import javax.crypto.NoSuchPaddingException

@RunWith(RobolectricTestRunner::class)
class ImageEncryptionManagerTest {

    @MockK
    private lateinit var mockEncryptionKeyAPI: EncryptionKeyAPI

    @MockK
    private lateinit var mockAndroidKeyStoreKeyProvider: AndroidKeyStoreKeyProvider

    @MockK
    private lateinit var mockCipher: Cipher

    @MockK
    private lateinit var mockFile: File

    @MockK
    private lateinit var mockFileInputStream: FileInputStream

    @MockK
    private lateinit var mockFileOutputStream: FileOutputStream

    @MockK
    private lateinit var mockCipherInputStream: CipherInputStream

    @MockK
    private lateinit var mockCipherOutputStream: CipherOutputStream

    @MockK
    private lateinit var mockBitmap: Bitmap

    private lateinit var imageEncryptionManager: ImageEncryptionManager

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        // Mock the encryption key API initialization
        every { mockEncryptionKeyAPI.initialize() } returns Unit
        every { mockEncryptionKeyAPI.isKeyAvailable() } returns true
        every { mockEncryptionKeyAPI.refreshKey() } returns true

        // Mock the AndroidKeyStoreKeyProvider initialization
        every { mockAndroidKeyStoreKeyProvider.initialize() } returns Unit
        every { mockAndroidKeyStoreKeyProvider.isKeyAvailable() } returns true
        every { mockAndroidKeyStoreKeyProvider.refreshKey() } returns true

        imageEncryptionManager = ImageEncryptionManager(mockEncryptionKeyAPI)
    }

    @Test
    fun `initialization should call encryptionKeyAPI initialize`() {
        // Given
        val mockAPI = mockk<EncryptionKeyAPI>(relaxed = true)
        every { mockAPI.isKeyAvailable() } returns true

        // When
        ImageEncryptionManager(mockAPI)

        // Then
        verify { mockAPI.initialize() }
        verify { mockAPI.isKeyAvailable() }
    }

    @Test
    fun `initialization should refresh key when not available`() {
        // Given
        val mockAPI = mockk<EncryptionKeyAPI>(relaxed = true)
        every { mockAPI.isKeyAvailable() } returns false
        every { mockAPI.refreshKey() } returns true

        // When
        ImageEncryptionManager(mockAPI)

        // Then
        verify { mockAPI.initialize() }
        verify { mockAPI.isKeyAvailable() }
        verify { mockAPI.refreshKey() }
    }

    @Test
    fun `getCipher should return cipher from AndroidKeyStoreKeyProvider`() {
        // Given
        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } returns mockCipher
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        // When - using reflection to test private method
        val getCipherMethod = ImageEncryptionManager::class.java.getDeclaredMethod("getCipher", Int::class.java)
        getCipherMethod.isAccessible = true
        val result = getCipherMethod.invoke(manager, Cipher.ENCRYPT_MODE)

        // Then
        assertThat(result).isEqualTo(mockCipher)
    }

    @Test(expected = IllegalStateException::class)
    fun `getCipher should throw exception when encryptionKeyAPI is not AndroidKeyStoreKeyProvider`() {
        // Given
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI)

        // When - using reflection to test private method
        val getCipherMethod = ImageEncryptionManager::class.java.getDeclaredMethod("getCipher", Int::class.java)
        getCipherMethod.isAccessible = true
        getCipherMethod.invoke(manager, Cipher.ENCRYPT_MODE)

        // Then - exception should be thrown
    }

    @Test
    fun `getEncryptedFile should append extension when not present`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI)

        // When - using reflection to test private method
        val getEncryptedFileMethod = ImageEncryptionManager::class.java.getDeclaredMethod("getEncryptedFile", String::class.java)
        getEncryptedFileMethod.isAccessible = true
        val result = getEncryptedFileMethod.invoke(manager, filePath) as File

        // Then
        assertThat(result.path).isEqualTo("/path/to/image.jpg.encrypted")
    }

    @Test
    fun `getEncryptedFile should not append extension when already present`() {
        // Given
        val filePath = "/path/to/image.jpg.encrypted"
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI)

        // When - using reflection to test private method
        val getEncryptedFileMethod = ImageEncryptionManager::class.java.getDeclaredMethod("getEncryptedFile", String::class.java)
        getEncryptedFileMethod.isAccessible = true
        val result = getEncryptedFileMethod.invoke(manager, filePath) as File

        // Then
        assertThat(result.path).isEqualTo("/path/to/image.jpg.encrypted")
    }

    @Test
    fun `encryptImage with filePath should create FileInputStream and call overloaded method`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        mockkStatic(FileInputStream::class)
        mockkStatic(File::class)

        every { File(filePath) } returns mockFile
        every { FileInputStream(mockFile) } returns mockFileInputStream
        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } returns mockCipher
        every { mockFileInputStream.copyTo(any()) } returns 0L
        every { mockFileInputStream.close() } returns Unit
        every { mockCipherOutputStream.flush() } returns Unit
        every { mockCipherOutputStream.close() } returns Unit

        mockkStatic(FileOutputStream::class)
        every { FileOutputStream(any<File>()) } returns mockFileOutputStream

        mockkStatic(CipherOutputStream::class)
        every { CipherOutputStream(mockFileOutputStream, mockCipher) } returns mockCipherOutputStream

        every { File("/path/to/image.jpg.encrypted").exists() } returns true

        // When
        val result = manager.encryptImage(filePath)

        // Then
        assertThat(result).isTrue()
        verify { FileInputStream(mockFile) }
    }

    @Test
    fun `encryptImage with InputStream should encrypt and return true when successful`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } returns mockCipher

        mockkStatic(FileOutputStream::class)
        every { FileOutputStream(any<File>()) } returns mockFileOutputStream

        mockkStatic(CipherOutputStream::class)
        every { CipherOutputStream(mockFileOutputStream, mockCipher) } returns mockCipherOutputStream
        every { mockCipherOutputStream.flush() } returns Unit
        every { mockCipherOutputStream.close() } returns Unit

        every { File("/path/to/image.jpg.encrypted").exists() } returns true

        // When
        val result = manager.encryptImage(filePath, inputStream)

        // Then
        assertThat(result).isTrue()
        verify { CipherOutputStream(mockFileOutputStream, mockCipher) }
    }

    @Test
    fun `encryptImage should return false when encrypted file does not exist`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } returns mockCipher

        mockkStatic(FileOutputStream::class)
        every { FileOutputStream(any<File>()) } returns mockFileOutputStream

        mockkStatic(CipherOutputStream::class)
        every { CipherOutputStream(mockFileOutputStream, mockCipher) } returns mockCipherOutputStream
        every { mockCipherOutputStream.flush() } returns Unit
        every { mockCipherOutputStream.close() } returns Unit

        every { File("/path/to/image.jpg.encrypted").exists() } returns false

        // When
        val result = manager.encryptImage(filePath, inputStream)

        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `decryptImage should return bitmap when successful`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.DECRYPT_MODE) } returns mockCipher

        mockkStatic(FileInputStream::class)
        every { FileInputStream(any<File>()) } returns mockFileInputStream

        mockkStatic(CipherInputStream::class)
        every { CipherInputStream(mockFileInputStream, mockCipher) } returns mockCipherInputStream
        every { mockCipherInputStream.close() } returns Unit
        every { mockFileInputStream.close() } returns Unit

        mockkStatic(BitmapFactory::class)
        every { BitmapFactory.decodeStream(mockCipherInputStream) } returns mockBitmap

        // When
        val result = manager.decryptImage(filePath)

        // Then
        assertThat(result).isEqualTo(mockBitmap)
        verify { BitmapFactory.decodeStream(mockCipherInputStream) }
    }

    @Test
    fun `decryptImageToByteArray should return byte array when successful`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val expectedBytes = "decrypted data".toByteArray()
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.DECRYPT_MODE) } returns mockCipher

        mockkStatic(FileInputStream::class)
        every { FileInputStream(any<File>()) } returns mockFileInputStream

        mockkStatic(CipherInputStream::class)
        every { CipherInputStream(mockFileInputStream, mockCipher) } returns mockCipherInputStream
        every { mockCipherInputStream.readBytes() } returns expectedBytes
        every { mockCipherInputStream.close() } returns Unit
        every { mockFileInputStream.close() } returns Unit

        // When
        val result = manager.decryptImageToByteArray(filePath)

        // Then
        assertThat(result).isEqualTo(expectedBytes)
        verify { mockCipherInputStream.readBytes() }
    }

    @Test
    fun `FILE_EXTENSION constant should be encrypted`() {
        // Then
        assertThat(ImageEncryptionManager.FILE_EXTENSION).isEqualTo("encrypted")
    }

    @Test(expected = IOException::class)
    fun `encryptImage should throw IOException when file operations fail`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } returns mockCipher

        mockkStatic(FileOutputStream::class)
        every { FileOutputStream(any<File>()) } throws IOException("File not found")

        // When
        manager.encryptImage(filePath, inputStream)

        // Then - exception should be thrown
    }

    @Test(expected = InvalidKeyException::class)
    fun `encryptImage should throw InvalidKeyException when cipher initialization fails`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } throws InvalidKeyException("Invalid key")

        // When
        manager.encryptImage(filePath, inputStream)

        // Then - exception should be thrown
    }

    @Test(expected = NoSuchAlgorithmException::class)
    fun `encryptImage should throw NoSuchAlgorithmException when algorithm not available`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } throws NoSuchAlgorithmException("Algorithm not found")

        // When
        manager.encryptImage(filePath, inputStream)

        // Then - exception should be thrown
    }

    @Test(expected = NoSuchPaddingException::class)
    fun `encryptImage should throw NoSuchPaddingException when padding not available`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } throws NoSuchPaddingException("Padding not found")

        // When
        manager.encryptImage(filePath, inputStream)

        // Then - exception should be thrown
    }

    @Test
    fun `decryptImage should return null when BitmapFactory fails to decode`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.DECRYPT_MODE) } returns mockCipher

        mockkStatic(FileInputStream::class)
        every { FileInputStream(any<File>()) } returns mockFileInputStream

        mockkStatic(CipherInputStream::class)
        every { CipherInputStream(mockFileInputStream, mockCipher) } returns mockCipherInputStream
        every { mockCipherInputStream.close() } returns Unit
        every { mockFileInputStream.close() } returns Unit

        mockkStatic(BitmapFactory::class)
        every { BitmapFactory.decodeStream(mockCipherInputStream) } returns null

        // When
        val result = manager.decryptImage(filePath)

        // Then
        assertThat(result).isNull()
    }

    @Test(expected = IOException::class)
    fun `decryptImage should throw IOException when file operations fail`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.DECRYPT_MODE) } returns mockCipher

        mockkStatic(FileInputStream::class)
        every { FileInputStream(any<File>()) } throws IOException("File not found")

        // When
        manager.decryptImage(filePath)

        // Then - exception should be thrown
    }

    @Test(expected = IOException::class)
    fun `decryptImageToByteArray should throw IOException when file operations fail`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.DECRYPT_MODE) } returns mockCipher

        mockkStatic(FileInputStream::class)
        every { FileInputStream(any<File>()) } throws IOException("File not found")

        // When
        manager.decryptImageToByteArray(filePath)

        // Then - exception should be thrown
    }

    @Test
    fun `encryptImage should properly close streams in finally block`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val inputStream = mockk<InputStream>(relaxed = true)
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } returns mockCipher

        mockkStatic(FileOutputStream::class)
        every { FileOutputStream(any<File>()) } returns mockFileOutputStream

        mockkStatic(CipherOutputStream::class)
        every { CipherOutputStream(mockFileOutputStream, mockCipher) } returns mockCipherOutputStream
        every { mockCipherOutputStream.flush() } returns Unit
        every { mockCipherOutputStream.close() } returns Unit
        every { inputStream.close() } returns Unit
        every { inputStream.copyTo(any()) } throws IOException("Copy failed")

        every { File("/path/to/image.jpg.encrypted").exists() } returns false

        // When
        try {
            manager.encryptImage(filePath, inputStream)
        } catch (e: IOException) {
            // Expected
        }

        // Then
        verify { mockCipherOutputStream.flush() }
        verify { mockCipherOutputStream.close() }
        verify { inputStream.close() }
    }

    @Test
    fun `decryptImage should properly close streams in finally block`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.DECRYPT_MODE) } returns mockCipher

        mockkStatic(FileInputStream::class)
        every { FileInputStream(any<File>()) } returns mockFileInputStream

        mockkStatic(CipherInputStream::class)
        every { CipherInputStream(mockFileInputStream, mockCipher) } returns mockCipherInputStream
        every { mockCipherInputStream.close() } returns Unit
        every { mockFileInputStream.close() } returns Unit

        mockkStatic(BitmapFactory::class)
        every { BitmapFactory.decodeStream(mockCipherInputStream) } throws RuntimeException("Decode failed")

        // When
        try {
            manager.decryptImage(filePath)
        } catch (e: RuntimeException) {
            // Expected
        }

        // Then
        verify { mockCipherInputStream.close() }
        verify { mockFileInputStream.close() }
    }
}
